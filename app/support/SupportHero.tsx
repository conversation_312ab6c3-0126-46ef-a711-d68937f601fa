
'use client';

import { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

export default function SupportHero() {
  const { t } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');

  const supportOptions = [
    {
      title: t('support.hero.supportOptions.technical.title'),
      description: t('support.hero.supportOptions.technical.description'),
      icon: 'ri-customer-service-line',
      color: 'from-blue-500 to-blue-600',
      availability: t('support.hero.supportOptions.technical.availability'),
      responseTime: t('support.hero.supportOptions.technical.responseTime')
    },
    {
      title: t('support.hero.supportOptions.developer.title'),
      description: t('support.hero.supportOptions.developer.description'),
      icon: 'ri-code-line',
      color: 'from-green-500 to-green-600',
      availability: t('support.hero.supportOptions.developer.availability'),
      responseTime: t('support.hero.supportOptions.developer.responseTime')
    },
    {
      title: t('support.hero.supportOptions.account.title'),
      description: t('support.hero.supportOptions.account.description'),
      icon: 'ri-user-settings-line',
      color: 'from-purple-500 to-purple-600',
      availability: t('support.hero.supportOptions.account.availability'),
      responseTime: t('support.hero.supportOptions.account.responseTime')
    },
    {
      title: t('support.hero.supportOptions.compliance.title'),
      description: t('support.hero.supportOptions.compliance.description'),
      icon: 'ri-shield-check-line',
      color: 'from-orange-500 to-orange-600',
      availability: t('support.hero.supportOptions.compliance.availability'),
      responseTime: t('support.hero.supportOptions.compliance.responseTime')
    }
  ];

  const quickLinks = [
    { title: t('support.hero.quickLinks.items.gettingStarted.title'), icon: 'ri-book-open-line', category: t('support.hero.quickLinks.items.gettingStarted.category') },
    { title: t('support.hero.quickLinks.items.apiDocs.title'), icon: 'ri-terminal-line', category: t('support.hero.quickLinks.items.apiDocs.category') },
    { title: t('support.hero.quickLinks.items.security.title'), icon: 'ri-shield-line', category: t('support.hero.quickLinks.items.security.category') },
    { title: t('support.hero.quickLinks.items.fees.title'), icon: 'ri-money-dollar-circle-line', category: t('support.hero.quickLinks.items.fees.category') },
    { title: t('support.hero.quickLinks.items.troubleshooting.title'), icon: 'ri-tools-line', category: t('support.hero.quickLinks.items.troubleshooting.category') },
    { title: t('support.hero.quickLinks.items.status.title'), icon: 'ri-pulse-line', category: t('support.hero.quickLinks.items.status.category') }
  ];

  const popularTopics = [
    t('support.hero.popularTopics.0'),
    t('support.hero.popularTopics.1'),
    t('support.hero.popularTopics.2'),
    t('support.hero.popularTopics.3'),
    t('support.hero.popularTopics.4')
  ];

  return (
    <section className="py-28 bg-white relative overflow-hidden">
      {/* 高级装饰背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/30 to-purple-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-green-200/30 to-blue-200/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-purple-200/30 to-pink-200/30 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>
      
      {/* 动态网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.05)_1px,transparent_1px)] bg-[size:80px_80px]"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8 shadow-lg">
            <i className="ri-customer-service-line mr-2"></i>
            {t('support.hero.tagline')}
          </div>

          <h1 className="text-7xl md:text-8xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8 leading-tight">
            {t('support.hero.title')}
          </h1>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            {t('support.hero.subtitle')}
          </p>

          {/* 搜索栏 */}
          <div className="max-w-2xl mx-auto mb-12">
            <div className="relative">
              <input
                type="text"
                placeholder={t('support.hero.searchPlaceholder')}
                className="w-full px-8 py-6 pl-16 text-lg text-gray-700 bg-white border-2 border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-xl"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <i className="ri-search-line absolute left-6 top-1/2 transform -translate-y-1/2 text-gray-400 text-2xl"></i>
              <button className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-200 cursor-pointer">
                {t('support.hero.searchButton')}
              </button>
            </div>
          </div>

          {/* 热门搜索 */}
          <div className="flex flex-wrap justify-center gap-3 mb-16">
            <span className="text-gray-600 text-sm">{t('support.hero.popularSearches')}</span>
            {popularTopics.map((topic, index) => (
              <button
                key={index}
                className="text-blue-600 hover:text-blue-800 text-sm bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-full transition-all duration-200 cursor-pointer"
              >
                {topic}
              </button>
            ))}
          </div>
        </div>
        
        {/* 支持选项 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-20">
          {supportOptions.map((option, index) => (
            <div key={index} className="group relative">
              <div className={`absolute inset-0 bg-gradient-to-r ${option.color} opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-2xl blur-xl`}></div>
              <div className="relative bg-white rounded-2xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 cursor-pointer">
                <div className={`w-16 h-16 bg-gradient-to-r ${option.color} rounded-2xl flex items-center justify-center mx-auto mb-6`}>
                  <i className={`${option.icon} text-white text-2xl`}></i>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3 text-center">{option.title}</h3>
                <p className="text-gray-600 text-center mb-6">{option.description}</p>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">{t('support.contact.serviceTime')}</span>
                    <span className="font-medium text-gray-900">{option.availability}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">{t('support.contact.responseTime')}</span>
                    <span className="font-medium text-gray-900">{option.responseTime}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* 快速链接 */}
        <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-100 mb-20">
          <h3 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            {t('support.hero.quickLinks.title')}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {quickLinks.map((link, index) => (
              <div key={index} className="group">
                <div className="flex flex-col items-center p-4 bg-gray-50 rounded-xl hover:bg-blue-50 transition-all duration-200 cursor-pointer">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-200">
                    <i className={`${link.icon} text-white text-xl`}></i>
                  </div>
                  <span className="text-sm font-medium text-gray-900 text-center">{link.title}</span>
                  <span className="text-xs text-gray-500 mt-1">{link.category}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* 联系我们 */}
        <div className="text-center bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-12 border border-gray-200">
          <h3 className="text-4xl font-bold text-gray-900 mb-6">
            {t('support.hero.needHelp.title')}
          </h3>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            {t('support.hero.needHelp.subtitle')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap cursor-pointer">
              <i className="ri-chat-smile-line mr-2"></i>
              {t('support.hero.needHelp.liveChat')}
            </button>
            <button className="border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-300 whitespace-nowrap cursor-pointer">
              <i className="ri-mail-line mr-2"></i>
              {t('support.hero.needHelp.sendEmail')}
            </button>
          </div>
          
          {/* 服务时间 */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className="p-4 bg-white rounded-xl shadow-md">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                <i className="ri-chat-smile-line text-white text-xl"></i>
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">{t('support.hero.needHelp.serviceHours.liveChat.title')}</h4>
              <p className="text-gray-600 text-sm">{t('support.hero.needHelp.serviceHours.liveChat.description')}</p>
            </div>
            <div className="p-4 bg-white rounded-xl shadow-md">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                <i className="ri-mail-line text-white text-xl"></i>
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">{t('support.hero.needHelp.serviceHours.email.title')}</h4>
              <p className="text-gray-600 text-sm">{t('support.hero.needHelp.serviceHours.email.description')}</p>
            </div>
            <div className="p-4 bg-white rounded-xl shadow-md">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                <i className="ri-phone-line text-white text-xl"></i>
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">{t('support.hero.needHelp.serviceHours.phone.title')}</h4>
              <p className="text-gray-600 text-sm">{t('support.hero.needHelp.serviceHours.phone.description')}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
