'use client';

import { useLanguage } from '@/contexts/LanguageContext';

export default function ContactOptions() {
  const { t } = useLanguage();

  const contactMethods = [
    {
      title: t('support.contact.methods.liveChat.title'),
      description: t('support.contact.methods.liveChat.description'),
      availability: t('support.contact.methods.liveChat.availability'),
      response: t('support.contact.methods.liveChat.response'),
      icon: 'ri-message-3-line',
      color: 'bg-blue-600',
      action: t('support.contact.methods.liveChat.action')
    },
    {
      title: t('support.contact.methods.email.title'),
      description: t('support.contact.methods.email.description'),
      availability: t('support.contact.methods.email.availability'),
      response: t('support.contact.methods.email.response'),
      icon: 'ri-mail-line',
      color: 'bg-green-600',
      action: t('support.contact.methods.email.action')
    },
    {
      title: t('support.contact.methods.phone.title'),
      description: t('support.contact.methods.phone.description'),
      availability: t('support.contact.methods.phone.availability'),
      response: t('support.contact.methods.phone.response'),
      icon: 'ri-phone-line',
      color: 'bg-purple-600',
      action: t('support.contact.methods.phone.action')
    },
    {
      title: t('support.contact.methods.technical.title'),
      description: t('support.contact.methods.technical.description'),
      availability: t('support.contact.methods.technical.availability'),
      response: t('support.contact.methods.technical.response'),
      icon: 'ri-code-line',
      color: 'bg-orange-600',
      action: t('support.contact.methods.technical.action')
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {t('support.contact.title')}
          </h2>
          <p className="text-xl text-gray-600">
            {t('support.contact.subtitle')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {contactMethods.map((method, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300">
              <div className="text-center">
                <div className={`w-16 h-16 ${method.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <i className={`${method.icon} text-white text-2xl`}></i>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {method.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {method.description}
                </p>
                
                <div className="space-y-2 mb-6">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">{t('support.contact.serviceTime')}</span>
                    <span className="text-gray-700">{method.availability}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">{t('support.contact.responseTime')}</span>
                    <span className="text-gray-700">{method.response}</span>
                  </div>
                </div>
                
                <button className={`w-full ${method.color} text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 whitespace-nowrap cursor-pointer`}>
                  {method.action}
                </button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 bg-gray-50 rounded-xl p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('support.contact.officeAddress.title')}
              </h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <i className="ri-map-pin-line text-gray-400 text-xl mr-3 mt-1"></i>
                  <div>
                    <h4 className="font-medium text-gray-900">{t('support.contact.officeAddress.headquarters.title')}</h4>
                    <p className="text-gray-600">{t('support.contact.officeAddress.headquarters.address')}</p>
                    <p className="text-gray-600">{t('support.contact.officeAddress.headquarters.fullAddress')}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <i className="ri-map-pin-line text-gray-400 text-xl mr-3 mt-1"></i>
                  <div>
                    <h4 className="font-medium text-gray-900">{t('support.contact.officeAddress.us.title')}</h4>
                    <p className="text-gray-600">{t('support.contact.officeAddress.us.address')}</p>
                    <p className="text-gray-600">{t('support.contact.officeAddress.us.fullAddress')}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <i className="ri-map-pin-line text-gray-400 text-xl mr-3 mt-1"></i>
                  <div>
                    <h4 className="font-medium text-gray-900">{t('support.contact.officeAddress.europe.title')}</h4>
                    <p className="text-gray-600">{t('support.contact.officeAddress.europe.address')}</p>
                    <p className="text-gray-600">{t('support.contact.officeAddress.europe.fullAddress')}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                {t('support.contact.quickLinks.title')}
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-book-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    {t('support.contact.quickLinks.items.docs')}
                  </a>
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-github-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    {t('support.contact.quickLinks.items.github')}
                  </a>
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-discord-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    {t('support.contact.quickLinks.items.discord')}
                  </a>
                </div>
                <div className="space-y-2">
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-file-text-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    {t('support.contact.quickLinks.items.status')}
                  </a>
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-bug-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    {t('support.contact.quickLinks.items.feedback')}
                  </a>
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-road-map-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    {t('support.contact.quickLinks.items.roadmap')}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}