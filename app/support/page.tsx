'use client';

import { Suspense } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SupportHero from './SupportHero';
import SearchSection from './SearchSection';
import FAQSection from './FAQSection';
import ContactOptions from './ContactOptions';
import { useLanguage } from '@/contexts/LanguageContext';

export default function Support() {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <Suspense fallback={
          <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
            {/* 升级的加载动画背景 */}
            <div className="absolute inset-0">
              <div className="absolute top-20 left-20 w-96 h-96 bg-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-green-400/15 rounded-full blur-2xl animate-pulse delay-2000"></div>
            </div>

            {/* 升级的支持装饰 */}
            <div className="absolute inset-0">
              <div className="absolute top-1/4 left-1/4 w-4 h-4 bg-blue-500/30 rounded-full animate-pulse"></div>
              <div className="absolute top-1/3 right-1/3 w-3 h-3 bg-purple-500/30 rounded-full animate-pulse delay-1000"></div>
              <div className="absolute bottom-1/4 left-1/3 w-5 h-5 bg-green-500/30 rounded-full animate-pulse delay-2000"></div>
            </div>

            <div className="text-center relative z-10">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse shadow-2xl">
                <i className="ri-customer-service-line text-white text-3xl"></i>
              </div>
              <div className="text-gray-600 font-medium text-2xl mb-4">{t('loading.support')}</div>
              <div className="flex justify-center mb-6">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
              <div className="text-sm text-gray-500">{t('loading.support')}</div>
            </div>
          </div>
        }>
          <SupportHero />
          <SearchSection />
          <FAQSection />
          <ContactOptions />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}