'use client';

import { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

export default function SearchSection() {
  const { t } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');

  const popularTopics = [
    t('support.search.popularTopics.0'),
    t('support.search.popularTopics.1'),
    t('support.search.popularTopics.2'),
    t('support.search.popularTopics.3'),
    t('support.search.popularTopics.4'),
    t('support.search.popularTopics.5'),
    t('support.search.popularTopics.6'),
    t('support.search.popularTopics.7')
  ];

  const handleSearch = (e: { preventDefault: () => void; }) => {
    e.preventDefault();
    // 搜索逻辑
    console.log('搜索:', searchQuery);
  };

  return (
    <section className="py-16 bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {t('support.search.title')}
          </h2>
          <p className="text-gray-600">
            {t('support.search.subtitle')}
          </p>
        </div>

        <form onSubmit={handleSearch} className="relative mb-8">
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('support.search.placeholder')}
              className="w-full px-6 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-16"
            />
            <button
              type="submit"
              className="absolute right-2 top-2 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap cursor-pointer"
            >
              <i className="ri-search-line"></i>
            </button>
          </div>
        </form>

        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('support.search.popularSearches')}
          </h3>
          <div className="flex flex-wrap justify-center gap-2">
            {popularTopics.map((topic, index) => (
              <button
                key={index}
                onClick={() => setSearchQuery(topic)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors duration-200 text-sm whitespace-nowrap cursor-pointer"
              >
                {topic}
              </button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}