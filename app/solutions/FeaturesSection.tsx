'use client';

import { useLanguage } from '@/contexts/LanguageContext';

export default function FeaturesSection() {
  const { t } = useLanguage();

  const features = [
    {
      icon: 'ri-shield-check-line',
      title: t('solutions.features.items.bankGradeSecurity.title'),
      description: t('solutions.features.items.bankGradeSecurity.description')
    },
    {
      icon: 'ri-speed-line',
      title: t('solutions.features.items.lightningFast.title'),
      description: t('solutions.features.items.lightningFast.description')
    },
    {
      icon: 'ri-global-line',
      title: t('solutions.features.items.globalCoverage.title'),
      description: t('solutions.features.items.globalCoverage.description')
    },
    {
      icon: 'ri-code-s-slash-line',
      title: t('solutions.features.items.easyIntegration.title'),
      description: t('solutions.features.items.easyIntegration.description')
    },
    {
      icon: 'ri-customer-service-line',
      title: t('solutions.features.items.support247.title'),
      description: t('solutions.features.items.support247.description')
    },
    {
      icon: 'ri-line-chart-line',
      title: t('solutions.features.items.realTimeMonitoring.title'),
      description: t('solutions.features.items.realTimeMonitoring.description')
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {t('solutions.features.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('solutions.features.subtitle')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center p-6 rounded-xl hover:bg-gray-50 transition-colors duration-300">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className={`${feature.icon} text-blue-600 text-2xl`}></i>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}