'use client';

import { useLanguage } from '@/contexts/LanguageContext';

export default function ClientsSection() {
  const { t } = useLanguage();

  const clients = [
    { name: t('solutions.clients.companies.techCorp'), logo: 'TC', industry: t('solutions.clients.industries.technology') },
    { name: t('solutions.clients.companies.globalTrade'), logo: 'GT', industry: t('solutions.clients.industries.trade') },
    { name: t('solutions.clients.companies.financeFirst'), logo: 'FF', industry: t('solutions.clients.industries.finance') },
    { name: t('solutions.clients.companies.gameStudio'), logo: 'GS', industry: t('solutions.clients.industries.gaming') },
    { name: t('solutions.clients.companies.mediaPlus'), logo: 'MP', industry: t('solutions.clients.industries.media') },
    { name: t('solutions.clients.companies.shopCenter'), logo: 'SC', industry: t('solutions.clients.industries.ecommerce') }
  ];

  const testimonials = [
    {
      quote: t('solutions.clients.testimonials.techCorp.quote'),
      author: t('solutions.clients.testimonials.techCorp.author'),
      company: t('solutions.clients.testimonials.techCorp.company'),
      role: t('solutions.clients.testimonials.techCorp.role')
    },
    {
      quote: t('solutions.clients.testimonials.globalTrade.quote'),
      author: t('solutions.clients.testimonials.globalTrade.author'),
      company: t('solutions.clients.testimonials.globalTrade.company'),
      role: t('solutions.clients.testimonials.globalTrade.role')
    },
    {
      quote: t('solutions.clients.testimonials.financeFirst.quote'),
      author: t('solutions.clients.testimonials.financeFirst.author'),
      company: t('solutions.clients.testimonials.financeFirst.company'),
      role: t('solutions.clients.testimonials.financeFirst.role')
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {t('solutions.clients.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('solutions.clients.subtitle')}
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 mb-16">
          {clients.map((client, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-lg font-bold text-gray-600">{client.logo}</span>
              </div>
              <p className="text-sm font-medium text-gray-900">{client.name}</p>
              <p className="text-xs text-gray-500">{client.industry}</p>
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-gray-50 rounded-xl p-6">
              <div className="mb-4">
                <i className="ri-double-quotes-l text-blue-600 text-2xl"></i>
              </div>
              <p className="text-gray-700 mb-4 italic">
                {testimonial.quote}
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                  <span className="text-white font-medium text-sm">
                    {testimonial.author.charAt(0)}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">{testimonial.author}</p>
                  <p className="text-sm text-gray-600">{testimonial.role}, {testimonial.company}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}