'use client';

import Link from 'next/link';
import Button from '@/components/ui/Button';
import { useLanguage } from '@/contexts/LanguageContext';

export default function CTASection() {
  const { t } = useLanguage();

  return (
    <section className="py-20 bg-blue-600 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-4xl font-bold mb-6">
          {t('solutions.cta.title')}
        </h2>
        <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
          {t('solutions.cta.subtitle')}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/contact">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              <i className="ri-phone-line w-5 h-5 flex items-center justify-center mr-2"></i>
              {t('solutions.cta.buttons.contactSales')}
            </Button>
          </Link>
          <Link href="/buy-crypto">
            <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10">
              <i className="ri-shopping-cart-line w-5 h-5 flex items-center justify-center mr-2"></i>
              {t('solutions.cta.buttons.tryNow')}
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}