'use client';

import { useLanguage } from '@/contexts/LanguageContext';

export default function UseCasesSection() {
  const { t } = useLanguage();

  const useCases = [
    {
      title: t('solutions.useCases.cases.ecommerce.title'),
      description: t('solutions.useCases.cases.ecommerce.description'),
      features: t('solutions.useCases.cases.ecommerce.features'),
      icon: 'ri-shopping-bag-line',
      image: 'https://readdy.ai/api/search-image?query=ecommerce%20online%20shopping%20platform%20interface%20with%20cryptocurrency%20payment%20options%2C%20modern%20shopping%20cart%20checkout%20process%2C%20digital%20payment%20integration%2C%20clean%20user%20interface%20design%2C%20product%20catalog%20display%2C%20secure%20payment%20gateway&width=400&height=300&seq=ecommerce-solution&orientation=landscape'
    },
    {
      title: t('solutions.useCases.cases.fintech.title'),
      description: t('solutions.useCases.cases.fintech.description'),
      features: t('solutions.useCases.cases.fintech.features'),
      icon: 'ri-bank-line',
      image: 'https://readdy.ai/api/search-image?query=fintech%20financial%20technology%20platform%20dashboard%20with%20cryptocurrency%20trading%20interface%2C%20banking%20services%20integration%2C%20regulatory%20compliance%20visualization%2C%20professional%20financial%20charts%20and%20analytics%2C%20secure%20digital%20banking%20environment&width=400&height=300&seq=fintech-solution&orientation=landscape'
    },
    {
      title: t('solutions.useCases.cases.saas.title'),
      description: t('solutions.useCases.cases.saas.description'),
      features: t('solutions.useCases.cases.saas.features'),
      icon: 'ri-cloud-line',
      image: 'https://readdy.ai/api/search-image?query=SaaS%20software%20platform%20dashboard%20with%20subscription%20billing%20interface%2C%20cloud%20services%20management%2C%20cryptocurrency%20payment%20integration%2C%20modern%20software%20interface%20design%2C%20subscription%20analytics%2C%20user%20management%20system&width=400&height=300&seq=saas-solution&orientation=landscape'
    },
    {
      title: t('solutions.useCases.cases.gaming.title'),
      description: t('solutions.useCases.cases.gaming.description'),
      features: t('solutions.useCases.cases.gaming.features'),
      icon: 'ri-gamepad-line',
      image: 'https://readdy.ai/api/search-image?query=gaming%20industry%20cryptocurrency%20payment%20system%20for%20virtual%20goods%2C%20game%20monetization%20platform%2C%20in-game%20purchase%20interface%2C%20digital%20gaming%20assets%2C%20modern%20gaming%20payment%20gateway%2C%20esports%20tournament%20rewards%20system&width=400&height=300&seq=gaming-solution&orientation=landscape'
    },
    {
      title: t('solutions.useCases.cases.digitalContent.title'),
      description: t('solutions.useCases.cases.digitalContent.description'),
      features: t('solutions.useCases.cases.digitalContent.features'),
      icon: 'ri-article-line',
      image: 'https://readdy.ai/api/search-image?query=digital%20content%20platform%20with%20cryptocurrency%20payment%20for%20creators%2C%20content%20monetization%20system%2C%20creator%20economy%20dashboard%2C%20digital%20media%20streaming%20interface%2C%20subscription%20content%20platform%2C%20modern%20content%20creation%20tools&width=400&height=300&seq=content-solution&orientation=landscape'
    },
    {
      title: t('solutions.useCases.cases.crossBorderTrade.title'),
      description: t('solutions.useCases.cases.crossBorderTrade.description'),
      features: t('solutions.useCases.cases.crossBorderTrade.features'),
      icon: 'ri-global-line',
      image: 'https://readdy.ai/api/search-image?query=international%20trade%20and%20cross-border%20payments%20with%20cryptocurrency%2C%20global%20business%20commerce%20platform%2C%20supply%20chain%20finance%20visualization%2C%20international%20shipping%20and%20logistics%2C%20modern%20trade%20finance%20interface&width=400&height=300&seq=trade-solution&orientation=landscape'
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {t('solutions.useCases.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('solutions.useCases.subtitle')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {useCases.map((useCase, index) => (
            <div key={index} className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
              <div className="h-48 bg-gray-200 relative">
                <img 
                  src={useCase.image} 
                  alt={useCase.title}
                  className="w-full h-full object-cover object-top"
                />
                <div className="absolute top-4 left-4 w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <i className={`${useCase.icon} text-white text-xl`}></i>
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {useCase.title}
                </h3>
                <p className="text-gray-600 mb-4 text-sm">
                  {useCase.description}
                </p>
                
                <ul className="space-y-2 mb-6">
                  {useCase.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-600">
                      <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                      {feature}
                    </li>
                  ))}
                </ul>
                
                <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                  {t('solutions.useCases.button')}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}