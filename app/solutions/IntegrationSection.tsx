'use client';

import { useLanguage } from '@/contexts/LanguageContext';

export default function IntegrationSection() {
  const { t } = useLanguage();

  const integrationMethods = [
    {
      title: t('solutions.integration.methods.widget.title'),
      description: t('solutions.integration.methods.widget.description'),
      icon: 'ri-widget-line',
      features: t('solutions.integration.methods.widget.features')
    },
    {
      title: t('solutions.integration.methods.restApi.title'),
      description: t('solutions.integration.methods.restApi.description'),
      icon: 'ri-code-line',
      features: t('solutions.integration.methods.restApi.features')
    },
    {
      title: t('solutions.integration.methods.hostedCheckout.title'),
      description: t('solutions.integration.methods.hostedCheckout.description'),
      icon: 'ri-secure-payment-line',
      features: t('solutions.integration.methods.hostedCheckout.features')
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {t('solutions.integration.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('solutions.integration.subtitle')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {integrationMethods.map((method, index) => (
            <div key={index} className="bg-white rounded-xl p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className={`${method.icon} text-blue-600 text-2xl`}></i>
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
                {method.title}
              </h3>
              
              <p className="text-gray-600 mb-6 text-center">
                {method.description}
              </p>
              
              <ul className="space-y-3 mb-8">
                {method.features.map((feature, idx) => (
                  <li key={idx} className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    {feature}
                  </li>
                ))}
              </ul>
              
              <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                {t('solutions.integration.button')}
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}