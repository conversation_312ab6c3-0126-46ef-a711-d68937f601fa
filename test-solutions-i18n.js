#!/usr/bin/env node

/**
 * Solutions 目录国际化测试脚本
 * 验证 app/solutions 目录下所有组件的翻译完整性
 */

const fs = require('fs');
const path = require('path');

// 读取翻译文件
function loadTranslations() {
  const enPath = path.join(__dirname, 'locales/en.json');
  const zhPath = path.join(__dirname, 'locales/zh.json');
  
  try {
    const en = JSON.parse(fs.readFileSync(enPath, 'utf8'));
    const zh = JSON.parse(fs.readFileSync(zhPath, 'utf8'));
    return { en, zh };
  } catch (error) {
    console.error('❌ 无法读取翻译文件:', error.message);
    process.exit(1);
  }
}

// 获取翻译值
function getTranslation(obj, key) {
  const keys = key.split('.');
  let value = obj;
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      return null;
    }
  }
  
  return value;
}

// 测试 Solutions 相关的翻译键
function testSolutionsTranslations() {
  console.log('🧪 测试 Solutions 目录国际化...\n');
  
  const { en, zh } = loadTranslations();
  
  // 需要测试的翻译键
  const testKeys = [
    // Hero Section
    'solutions.hero.tagline',
    'solutions.hero.title', 
    'solutions.hero.subtitle',
    'solutions.hero.solutions.ecommerce.title',
    'solutions.hero.solutions.financial.title',
    'solutions.hero.solutions.gaming.title',
    'solutions.hero.solutions.enterprise.title',
    'solutions.hero.stats.enterprises',
    'solutions.hero.stats.countries',
    'solutions.hero.stats.stability',
    'solutions.hero.stats.support',
    'solutions.hero.buttons.getStarted',
    'solutions.hero.buttons.scheduleDemo',
    'solutions.hero.buttons.contactExperts',
    'solutions.hero.buttons.watchDemo',
    'solutions.hero.cta.title',
    'solutions.hero.cta.subtitle',
    
    // Features Section
    'solutions.features.title',
    'solutions.features.subtitle',
    'solutions.features.items.bankGradeSecurity.title',
    'solutions.features.items.lightningFast.title',
    'solutions.features.items.globalCoverage.title',
    'solutions.features.items.easyIntegration.title',
    'solutions.features.items.support247.title',
    'solutions.features.items.realTimeMonitoring.title',
    
    // CTA Section
    'solutions.cta.title',
    'solutions.cta.subtitle',
    'solutions.cta.buttons.contactSales',
    'solutions.cta.buttons.tryNow',
    
    // Clients Section
    'solutions.clients.title',
    'solutions.clients.subtitle',
    'solutions.clients.companies.techCorp',
    'solutions.clients.industries.technology',
    'solutions.clients.testimonials.techCorp.quote',
    
    // Integration Section
    'solutions.integration.title',
    'solutions.integration.subtitle',
    'solutions.integration.methods.widget.title',
    'solutions.integration.methods.restApi.title',
    'solutions.integration.methods.hostedCheckout.title',
    'solutions.integration.button',
    
    // Use Cases Section
    'solutions.useCases.title',
    'solutions.useCases.subtitle',
    'solutions.useCases.cases.ecommerce.title',
    'solutions.useCases.cases.fintech.title',
    'solutions.useCases.cases.saas.title',
    'solutions.useCases.cases.gaming.title',
    'solutions.useCases.cases.digitalContent.title',
    'solutions.useCases.cases.crossBorderTrade.title',
    'solutions.useCases.button'
  ];
  
  let passedTests = 0;
  let failedTests = 0;
  
  console.log('🔍 测试翻译键存在性...\n');
  
  testKeys.forEach(key => {
    const enValue = getTranslation(en, key);
    const zhValue = getTranslation(zh, key);
    
    if (enValue && zhValue) {
      console.log(`✅ ${key}`);
      passedTests++;
    } else {
      console.log(`❌ ${key} - EN: ${enValue ? '✓' : '✗'}, ZH: ${zhValue ? '✓' : '✗'}`);
      failedTests++;
    }
  });
  
  console.log('\n📊 测试结果统计');
  console.log('========================================');
  console.log(`总测试数量: ${testKeys.length}`);
  console.log(`通过测试: ${passedTests}`);
  console.log(`失败测试: ${failedTests}`);
  console.log(`成功率: ${((passedTests / testKeys.length) * 100).toFixed(1)}%`);
  
  return failedTests === 0;
}

// 测试数组类型的翻译
function testArrayTranslations() {
  console.log('\n🔍 测试数组类型翻译...\n');
  
  const { en, zh } = loadTranslations();
  
  const arrayKeys = [
    'solutions.hero.solutions.ecommerce.features',
    'solutions.hero.solutions.financial.features',
    'solutions.hero.solutions.gaming.features',
    'solutions.hero.solutions.enterprise.features',
    'solutions.integration.methods.widget.features',
    'solutions.integration.methods.restApi.features',
    'solutions.integration.methods.hostedCheckout.features',
    'solutions.useCases.cases.ecommerce.features',
    'solutions.useCases.cases.fintech.features',
    'solutions.useCases.cases.saas.features'
  ];
  
  let arrayTestsPassed = 0;
  
  arrayKeys.forEach(key => {
    const enValue = getTranslation(en, key);
    const zhValue = getTranslation(zh, key);
    
    if (Array.isArray(enValue) && Array.isArray(zhValue) && enValue.length === zhValue.length) {
      console.log(`✅ ${key} (${enValue.length} items)`);
      arrayTestsPassed++;
    } else {
      console.log(`❌ ${key} - 数组长度不匹配或类型错误`);
    }
  });
  
  console.log(`\n数组翻译测试: ${arrayTestsPassed}/${arrayKeys.length} 通过`);
  
  return arrayTestsPassed === arrayKeys.length;
}

// 主函数
function main() {
  console.log('🌐 Solutions 目录国际化测试\n');
  
  const basicTestsPassed = testSolutionsTranslations();
  const arrayTestsPassed = testArrayTranslations();
  
  if (basicTestsPassed && arrayTestsPassed) {
    console.log('\n🎉 所有 Solutions 国际化测试通过！');
    console.log('✅ app/solutions 目录下的所有组件已成功国际化');
    process.exit(0);
  } else {
    console.log('\n⚠️  发现问题，请检查翻译文件');
    process.exit(1);
  }
}

// 运行测试
main();
